<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'CFMUTUIEVOL',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'cf-mutui-evolution-06-2025.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'CF Mutui Evolution',
        'version' => '1.0.0',
        'description' => "Scheda Raccolta dati CF Mutui Evolution",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new UserOverlay(1, 42, 40, ['properties' => ['name', 'lastname'],]),
            /*new SubjectOverlay(1, 37, 60, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(1, 120, 60, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 38.5, 64.5, [
                'role' => 'contractor',
                'method' => 'getPrintableBirthdate',
            ]),*/
            //new TextOverlay(1, 110, 64.5, [], "@test"),
            new ArrayOverlay(1, 110, 75, [
                'key' => 'indirizzoImmobile.street',
            ]),

            new RadioOverlay(1, 100, 70, [
                'key' => 'combinazione',
                'options' => [
                    '1' => new TextOverlay(1, 20.25, 61.75, [], "x"),
                    '3' => new TextOverlay(1, 55.25, 61.75, [], "x"),
                    '5' => new TextOverlay(1, 20.25, 67.5, [], "x"),
                    '6' => new TextOverlay(1, 55.25, 67.5, [], "x"),
                    '8' => new TextOverlay(1, 20, 72.25, [], "x"),
                ],
            ]),
            new RadioOverlay(1, 100, 70, [
                'key' => 'intermediazione',
                'options' => [
                    '0' => new TextOverlay(1, 164.50, 61.75, [], "x"),
                    '1' => new TextOverlay(1, 153.25, 61.75, [], "x"),
                ],
            ]),
            new ArrayOverlay(1, 165, 67.5, [
                'key' => 'importoIntermediazione',
            ]),
            
        ],
    ],

    'policy' => [
        'title' => 'CF Mutui Evolution - Proposta assicurativa',
        'signers' => ["contractor"], 
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    'quote' => [
        'net' => true,

        'fields' => [
            'combinazione',
            'capitale',
            'durata',
        ],
    ],

    'formRules' =>[
        // Dati generali
        'combinazione' => 'required|string',
        'intermediazione' => 'required|string',
        'importoIntermediazione' => 'nullable|numeric|min:0|required_if:intermediazione,1',
        'emissione' => 'nullable|numeric|min:0|required_if:intermediazione,1',

        // Dati mutuo
        'importoFinanziato' => 'required|numeric|min:0',
        'banca' => 'required|string',
        'durata' => 'required|numeric|min:0',
        'dataErogazione' => 'required|date',
        'notaio' => 'required|string',

        // Dati immobile
        'tipoAbitazione' => 'required|string',
        'indirizzoImmobile.type' => 'required|string',
        'indirizzoImmobile.street' => 'required|string',
        'indirizzoImmobile.number' => 'required|string',
        'indirizzoImmobile.zip' => 'required|string',
        'indirizzoImmobile.city' => 'required|string',
        'indirizzoImmobile.province' => 'required|string',
        'indirizzoImmobile.region' => 'required|string',
        'indirizzoImmobile.country' => 'required|string',
        'piano' => 'required|numeric|min:0',
        'interno' => 'required|string',

        // Dati catastali
        'datiCatastali.foglio' => 'required|string',
        'datiCatastali.part' => 'required|string',
        'datiCatastali.sub' => 'required|string',
        'datiCatastali.cat' => 'required|string',
        'datiCatastali.classe' => 'required|string',
        'datiCatastali.consist' => 'required|string',
        'datiCatastali.rendita' => 'required|numeric|min:0',

        // Dati assicurato
        'datiAssicurato.nome' => 'required|string',
        'datiAssicurato.cognome' => 'required|string',
        'datiAssicurato.via' => 'required|string',
        'datiAssicurato.numVia' => 'required|string',
        'datiAssicurato.cap' => 'required|string',
        'datiAssicurato.localita' => 'required|string',
        'datiAssicurato.provincia' => 'required|string',
        'datiAssicurato.telefono' => 'required|string',
        'datiAssicurato.email' => 'required|email',

        // Dichiara inoltre
        'dichiarazione1' => 'required|boolean',
        'dichiarazione2' => 'required|boolean',
        'dichiarazione3' => 'required|boolean',
    ]
];