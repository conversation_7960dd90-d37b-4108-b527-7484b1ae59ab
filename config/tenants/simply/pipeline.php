<?php

use App\Events\PrivacySigned;
use Upnovation\Easyprofile\Signature\IGSign\IGSign;
use Upnovation\Easyprofile\Signature\IGSign\IGSignClient;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;

return [
    'tasks' => [
        [
            'type' => 'client', 
            'dependson' => null,

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Client\ClientController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Client\ClientManager', 
            'template' => 'Tasks/Client',
            'name' => 'Anagrafica',

            'config' => [
                'compile' => ['mup', 'privacy', 'consent-digital-sending'],
                'dataInjection' => 'getPersonPreferences'
            ]
        ],
        /*[
            'type' => 'signature', 
            'dependson' => 'client',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
            'template' => 'Tasks/Signature',

            'config' => [
                'service' => IGSign::class,
                'signatureDeadlineInDays' => 3,
                'signatureType' => SignatureManager::$SIGNATURE_SIMPLE, // or SignatureManager::$SIGNATURE_FEA
                'folderTitle' => 'Documentazione precontrattuale',
                'folderDescription' => 'Documentazione precontrattuale',
                'documents' => ['privacy', 'assignment', 'consent-digital-sending'],
                'attachments' => ['mup'],
                'finalizationEvents' => [PrivacySigned::class],
            ]
        ],*/
        [
            'type' => 'survey', 
            'dependson' => 'client',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyManager', 
            'template' => 'Tasks/Survey',
        ],
        [
            'type' => 'mapper', 
            'dependson' => 'survey',
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager', 
            'template' => 'Tasks/ProductMapper',
            
            'config' => [
                'compile' => ['demands-and-needs'],
            ]
        ],
        [
            // FEA 2
            'type' => 'signature', 
            'dependson' => 'survey',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
            'template' => 'Tasks/Signature',

            'config' => [
                'service' => IGSign::class,
                'signatureDeadlineInDays' => 3,
                'signatureType' => SignatureManager::$SIGNATURE_FEA,
                'authentication' => IGSignClient::$AUTH_TYPE_SMS, // or IGSignClient::$AUTH_TYPE_EMAIL
                'folderTitle' => 'Documentazione precontrattuale',
                'folderDescription' => 'Documentazione precontrattuale',
                'documents' => ['privacy', 'assignment', 'consent-digital-sending', 'demands-and-needs'],
                'attachments' => ['mup'],
                'finalizationEvents' => [PrivacySigned::class],
                'options' => [

                ],
            ]
        ],
        [
            'type' => 'issuance', 
            'dependson' => 'signature',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Issuance\IssuanceController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager', 
            'template' => 'Tasks/Issuance',

            'config' => [
                'compile' => ['assignment', 'receipt-statement'],

                // Retrieve posted broker fee.
                'dataInjection' => 'getIssuanceData',
            ]
        ],
        // FEA 3
        [
            'type' => 'signature', 
            'dependson' => 'issuance',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
            'template' => 'Tasks/Signature',

            'config' => [
                // 'signers' => ['contractor'], maybe should be in the Document / SignatureBatch architeture.
                'service' => IGSign::class,
                'signatureDeadlineInDays' => 3,
                'signatureType' => SignatureManager::$SIGNATURE_FEA, // or SignatureManager::$SIGNATURE_FEA
                'authentication' => IGSignClient::$AUTH_TYPE_SMS, // or IGSignClient::$AUTH_TYPE_EMAIL
                'folderTitle' => 'Documentazione contrattuale',
                'folderDescription' => 'Documentazione contrattuale',
                'documents' => ['product-policy', 'assignment', 'receipt-statement'],
                'attachments' => [],

                // maybe dump this one.
                // @check
                'dataInjection' => 'getDataForDocument'
            ]
        ]
    ],
];