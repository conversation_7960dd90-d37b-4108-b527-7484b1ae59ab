<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Document;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;
use Tests\Unit\Pdf\TestDocumentSeeder;

class PdfToolsController extends Controller
{
    protected DocumentsInstaller $installer;

    protected PdfProcessor $pdfProcessor;

    public function __construct(DocumentsInstaller $installer, PdfProcessor $pdfProcessor)
    {
        $this->installer = $installer;

        $this->pdfProcessor = $pdfProcessor;
    }

    public function getDocument(Document $document, ?Pipeline $pipeline = null)
    {
        if (! $config = $document->loadConfiguration()) {
            dd("Document configuration for {$document->title} not found.");
        }

        if (! file_exists($config['file']->getFullPath())) {
            dd("Document file for {$document->title} not found.");
        }

        // invoke seeder
        if (! $pipeline) {
            $pipeline = (new TestDocumentSeeder())->makePipeline();
        }

        // Disabled: there's a fix in Document model that forces overlays
        // to be read from configuration.
        //$document->overlayArray = $config['document']['overlayArray'];

        $outfile = $this->pdfProcessor->compile(
            $document,
            $pipeline,
            new File([
                'disk' => 'documents',
                'path' => 'compiled',
                'filename' => 'test-' . $config['file']->filename,
            ]),
            [
                // Dati generali
                'combinazione' => '5',
                'intermediazione' => '0',
                'importoIntermediazione' => 1200.50,
                'emissione' => 300.00,

                // Dati mutuo
                'importoFinanziato' => 150000,
                'banca' => 'Banca Popolare di Milano',
                'durata' => 20,
                'dataErogazione' => '2024-06-15',
                'notaio' => 'Mario Rossi',

                // Dati immobile
                'tipoAbitazione' => 'appartamento',
                'indirizzoImmobile.type' => 'Residenziale',
                'indirizzoImmobile.street' => 'Via Garibaldi',
                'indirizzoImmobile.number' => '12B',
                'indirizzoImmobile.zip' => '20121',
                'indirizzoImmobile.city' => 'Milano',
                'indirizzoImmobile.province' => 'MI',
                'indirizzoImmobile.region' => 'Lombardia',
                'indirizzoImmobile.country' => 'Italia',
                'piano' => 3,
                'interno' => '7',
                'importoDaAssicurare' => 100000,

                // Dati catastali
                'datiCatastaliImmobile.foglio' => '123',
                'datiCatastaliImmobile.part' => '456',
                'datiCatastaliImmobile.sub' => '7',
                'datiCatastaliImmobile.cat' => 'A/2',
                'datiCatastaliImmobile.classe' => '3',
                'datiCatastaliImmobile.consist' => '5',
                'datiCatastaliImmobile.rendita' => 850.75,

                // Dati assicurato
                'datiAssicurato.nome' => 'Giovanni',
                'datiAssicurato.cognome' => 'Bianchi',
                'datiAssicurato.via' => 'Via Verdi',
                'datiAssicurato.numVia' => '8',
                'datiAssicurato.cap' => '20122',
                'datiAssicurato.localita' => 'Milano',
                'datiAssicurato.provincia' => 'MI',
                'datiAssicurato.telefono' => '3291234567',
                'datiAssicurato.email' => '<EMAIL>',

                // Dichiara inoltre
                'dichiarazione1' => '0',
                'dichiarazione2' => '0',
                'dichiarazione3' => '0',
            ]

        );

        return response()->file($outfile->getFullPath());
    }
}

