<?php

use App\Http\Controllers\ClientsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DoroteaAuthController;
use App\Http\Controllers\DoroteaSummaryController;
use App\Http\Controllers\PdfToolsController;
use App\Http\Controllers\PipelineController;
use App\Http\Controllers\PlaceholderYouSignContorller;
use App\Http\Controllers\SalesmenController;
use App\Models\Client;
use App\Models\Document;
use App\Models\NetworkNode;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\Task;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Laravel\Fortify\Http\Controllers\AuthenticatedSessionController;
use Nahid\JsonQ\Jsonq;
use Upnovation\DocumentReader\Providers\ConfigLoader;
use Upnovation\DocumentReader\Providers\Readers\AzureIdReader;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Tasks\Client\ClientController;
use Upnovation\Easyprofile\Tasks\Issuance\IssuanceController;
use Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager;
use Upnovation\Easyprofile\Tasks\Issuance\Processors\FormIssuanceController;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController;
use Upnovation\Easyprofile\Tasks\Signature\SignatureController;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;
use Upnovation\Easyprofile\Tasks\Summary\SummaryController;
use Upnovation\Easyprofile\Tasks\Survey\SurveyController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::inertia('/new-password', 'PasswordReset');

Route::get('/', [DashboardController::class, 'getIndex'])->middleware(['auth'])->name('dashboard');

Route::post('pipeline', [PipelineController::class, 'postIndex'])->middleware(['auth', 'can:pipeline.operate'])->name('pipeline.start');
Route::get('pipeline/{pipeline}/resume', [PipelineController::class, 'getResume'])->middleware(['auth', 'can:pipeline.resume,pipeline'])->name('pipeline.resume');
Route::get('pipeline/{pipeline}/next', [PipelineController::class, 'getNext'])->middleware(['auth', 'can:pipeline.operate'])->name('pipeline.next');
Route::get('pipeline/{pipeline}/task/{task}', [PipelineController::class, 'getGoto'])->middleware(['auth', 'can:pipeline.operate']);
Route::delete('pipeline/{pipeline}', [PipelineController::class, 'deleteIndex'])->middleware(['auth', 'can:pipeline.delete']);
Route::get('pipeline/{pipeline}', [PipelineController::class, 'getIndex'])->middleware(['auth', 'can:pipeline.view.any'])->name('pipeline.view');


//
//
// Dorotea actions
//
//
Route::get('dorotea/auth', [DoroteaAuthController::class, 'getAuth'])->middleware(['tenant.is:dorotea']);
Route::post('dorotea/{pipeline}/summary', [DoroteaSummaryController::class, 'postIndex'])->middleware(['auth', 'tenant.is:dorotea']);

//
//
// Survey actions
//
//
Route::get('tasks/survey/{pipeline}/{task}', [SurveyController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache', 'can:pipeline.operate']);
Route::post('tasks/survey/{pipeline}/{task}', [SurveyController::class, 'postIndex']);

//
//
// Mapper actions
//
//
Route::get('tasks/mapper/{pipeline}/{task}', [ProductMapperController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache', 'can:pipeline.operate']);
Route::post('tasks/mapper/{pipeline}/{task}', [ProductMapperController::class, 'postProducts']);

//
//
// Summary actions
//
//
Route::get('tasks/summary/{pipeline}/{task}', [SummaryController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);

//
//
// Client actions
//
//
Route::get('clients', [ClientsController::class, 'getIndex'])->middleware(['auth'])->name('clients');

Route::get('tasks/client/{pipeline}/{task}', [ClientController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache'])->name('tasks.client');
Route::post('tasks/client/{pipeline}/{task}', [ClientController::class, 'postIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::put('tasks/client/{pipeline}/{task}', [ClientController::class, 'putIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::get('clients/search/{query}', [ClientController::class, 'getSearch'])->middleware(['auth', 'nocache']);

//
//
// Signature actions
//
//
Route::get('tasks/signature/{pipeline}/{task}', [SignatureController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache'])->name('tasks.signature');

// Signature module.
Route::group(['prefix' => 'sign', 'middleware' => ['tenant.is:' . config('easyprofile.modules.signature.enabledTenants')]], function(){
    if (in_array(env('APP_ENV'), ['local'])) {
        Route::get('fake/{task}', function(Task $task){
            $task->addData('signatureStatus', 'done');
            $task->addData('folder', null);
            $task->save();

            app()->make(FileManager::class)->deletePipelineSignableFiles($task->pipeline);

            return redirect()->route('pipeline.next', [
                'pipeline' => $task->pipeline, 
            ]);
        });
    }

    Route::get('/{pipeline}/{task}', [SignatureController::class, 'getSign'])->middleware(['auth',]);
});

Route::get('signature/{pipeline}/{task}/callback/igs/{folderId}/{eventId}', [SignatureController::class, 'getIGSignCallback'])->name('signature.callback.igsign');

//
//
// Issuance actions
//
//
Route::get('tasks/issuance/{pipeline}/{task}/product/{product}', [IssuanceController::class, 'getIssue'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
//middlewares commented just to test.
Route::post('tasks/issuance/{pipeline}/{task}/upload', [IssuanceController::class, 'postUpload'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::get('tasks/issuance/{pipeline}/{task}', [IssuanceController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache'])->name('tasks.issuance');
Route::post('tasks/issuance/{pipeline}/{task}', [IssuanceController::class, 'postIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);

Route::get('issue/{pipeline}/{task}/{issuance}', [FormIssuanceController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'nocache'])->name('issue.form');
Route::post('issue/{pipeline}/{task}/{issuance}', [FormIssuanceController::class, 'postIndex'])->middleware(['auth', 'task.accessible', 'nocache'])->name('issue.form.post');

//
//
// PDF tools
//
//
if (in_array(env('APP_ENV'), ['local'])) {
    Route::get('tools/pdf/{document}/{pipeline?}', [PdfToolsController::class, 'getDocument'])->middleware(['auth', 'nocache']);
}

// @TODO whether the upload will work as standalone. Non used for now.
//Route::post('clients/id/upload', [ClientController::class, 'postUpload'])->middleware(['auth']);

//
//
// APP actions / modules
//
//
Route::get('/salesmen', [SalesmenController::class, 'getIndex'])->middleware(['auth', 'can:salesman.administer']);
Route::get('/salesmen/{id}', [SalesmenController::class, 'getSalesman'])->middleware(['auth', 'can:salesman.administer']);
Route::post('/salesman/status/{userId}', [SalesmenController::class, 'postSalesman'])->middleware(['auth', 'can:salesman.administer']);

Route::get('/products', [\App\Http\Controllers\ProductsController::class, 'getIndex'])->middleware('auth');
Route::get('/products/{product}', [\App\Http\Controllers\ProductsController::class, 'getProduct'])->middleware('auth');

// Documents module.
Route::group(['prefix' => 'documents', 'middleware' => ['tenant.is:' . config('easyprofile.modules.documents.enabledTenants')]], function(){
    Route::get('/', [\App\Http\Controllers\DocumentsController::class, 'getIndex'])->middleware(['auth', 'can:documents.view']);
    Route::get('/{id}/template', [\App\Http\Controllers\DocumentsController::class, 'getTemplate'])->middleware(['auth', 'can:documents.view']);
    Route::get('/{uuid}', [\App\Http\Controllers\DocumentsController::class, 'getFile'])->middleware(['auth', 'can:documents.view']);
});

//
//
// GEO utils.
//
//
Route::get('geo/countries/{query}', function(Request $request, $query){
    // Load json from storage.
    $json = storage_path('app/private/countries.json');

    if (! file_exists($json)) {
        abort(404);
    }

    if (! $countries = json_decode(file_get_contents($json), true)) {
        Log::error("Error decoding countries JSON file.");

        abort(500);
    }

    if ($query = strtolower($query)) {
        $countries = array_filter($countries, function ($country) use ($query) {
            return str_contains(strtolower($country['name']), $query)
                || str_contains(strtolower($country['code']), $query);
        });

    $countries = array_values($countries); // reindicizza
}

    // Return the countries as a JSON response.
    return response()->json($countries);

})->middleware(['auth']);

Route::get('geo/{q}', function(Request $request, $query){
    /*$jsonq = new Jsonq(storage_path('app/geo.json'));
    
    $result = ($jsonq->from('data')->where('nome', 'contains', $query)->fetch());*/

    $qb = DB::connection('tenant')->table('gi_comuni_cap');

    if ($request->has('_')) {
        $qb->select('denominazione_ita', 'sigla_provincia', 'denominazione_provincia', 'denominazione_regione', 'cap');
        $qb->where('denominazione_ita', 'like', "%$query%");
        $qb->orderByRaw('LENGTH(denominazione_ita) ASC');
        //$qb->groupBy('denominazione_ita');
    } else {
        $qb->select('denominazione_ita', 'sigla_provincia', 'denominazione_provincia', 'denominazione_regione', 'cap');
        $qb->where('cap', 'like', "$query%");
    }
                //->groupBy('denominazione_ita', 'sigla_provincia', 'denominazione_provincia', 'denominazione_regione', 'cap')
    return $qb->get();

    // Fuck up to get it into an array.
    return array_values(json_decode($result, true));
})->middleware(['auth']);


//
//
// DEV routes.
//
//
if (in_array(env('APP_ENV'), ['local', 'staging']))
{
    Route::get('dorotea/debug/pipeline', [DoroteaAuthController::class, 'getDebugPipeline'])->middleware(['auth', 'tenant.is:dorotea']);
}

if (in_array(env('APP_ENV'), ['local', 'testing']))
{
    // for PeopleScope quick testing
    Route::get('_people/{user}', function(Request $request, User $user){
        
        auth()->login($user);
        return Person::find(27);
        return [Person::find(25), Client::find(27)];

        return [Client::find(1), Client::find(2)];

        return $user;
    });

    Route::get('tt', function(){
        $reader = new AzureIdReader(env('AZURE_OPENAI_ENDPOINT'), env('AZURE_OPENAI_APIKEY1'));

        try {
            dd($reader->analyzeFile(Storage::path('ID_RANIERI_FABRIZIO.jpg')));

            file_put_contents('testcie.txt', print_r($result['analyzeResult']['documents'][0], true), FILE_APPEND);
        } catch (Exception $e) {
            echo "Errore: " . $e->getMessage();
        }


    });

    //Route::get('yousign', [PlaceholderYouSignContorller::class, 'getIndex']);

    Route::get('testing/tenants', function(){
        return response(null, 201);
    })->middleware('tenant.is:dorotea,tenant1');

    Route::get('testing/task/accessible/{pipeline?}/{task?}', function(Pipeline $pipeline, Task $task){
        
    })->middleware('task.accessible');

    Route::get('testing/task/matches/{pipeline?}/{task?}', function(Pipeline $pipeline, Task $task){
        
    })->middleware('task.matches');

    Route::get("debug/admin", function(){
        Auth::guard('web')->login(User::find(2));

        return redirect("/");
    });

    Route::get("debug/", function(){
        Auth::guard('web')->login(User::find(1));

        return redirect("/");
    });
}

// Kind of hack to add a middleware to the 'login' route
// which is being added by Fortify. The goal here is to
// prevent arbitrary tenants to access the login route.
// In this case, the ips middleware is filtering by 
// whitelisted IP address.
//Route::prefix('login')->middleware('ips')->group(function(){
    // @TODO move in easyprofile service provider, like:
    
    // Identify route somehow
    //$route = Route::getRoutes()->getByName('login');
    //dump(Route::getRoutes()->getRoutesByName());
    //dump($route);

    // Add the required middleware
    //Route::getRoutes()->getIterator()[0]->middleware('ips');
    // -------------------------------------------------------
    
    // @FIXME workaround, adding the fortify action.
    Route::get('login', [AuthenticatedSessionController::class, 'create'])->middleware(['ips', 'guest'])->name('login');
//});

if (in_array(env('APP_ENV'), ['staging']))
{
    Route::get("staging/admin/" . env("VITE_STAGING_KEY"), function(){
        Auth::guard('web')->login(User::find(2));

        return redirect("/");
    });

    Route::get("staging/" . env("VITE_STAGING_KEY"), function(){
        Auth::guard('web')->login(User::find(1));

        return redirect("/");
    });
}
