<?php namespace Tests\Unit\Pdf\Overlays;

use Exception;
use Tests\TestCase;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;

class ArrayOverlayTest extends TestCase
{
    public function constructorProvider()
    {
        return [
            [Exception::class,  [],                  []],
            [Exception::class,  ['foo'],             []],
            ["",  ['key' => 'foo'],    []],
            ["",  ['key' => 'keyName'],    ['badName' => 'keyvalue']],
            ['keyvalue',        ['key' => 'keyName'],    ['keyName' => 'keyvalue']],
            ['',                ['key' => 'keyName'],    ['keyName' => '']],
            [' ',               ['key' => 'keyName'],    ['keyName' => ' ']],
        ];
    }

    public function dotNotationProvider()
    {
        return [
            // [expected_value, settings, data]
            ['Via Roma 123', ['key' => 'indirizzoImmobile.street'], [
                'indirizzoImmobile' => [
                    'street' => 'Via Roma 123',
                    'number' => '45',
                    'city' => 'Milano'
                ]
            ]],
            ['45', ['key' => 'indirizzoImmobile.number'], [
                'indirizzoImmobile' => [
                    'street' => 'Via Roma 123',
                    'number' => '45',
                    'city' => 'Milano'
                ]
            ]],
            ['', ['key' => 'indirizzoImmobile.nonexistent'], [
                'indirizzoImmobile' => [
                    'street' => 'Via Roma 123',
                    'number' => '45'
                ]
            ]],
            ['', ['key' => 'nonexistent.street'], [
                'indirizzoImmobile' => [
                    'street' => 'Via Roma 123'
                ]
            ]],
            // Test backward compatibility with simple keys
            ['simple_value', ['key' => 'simple_key'], [
                'simple_key' => 'simple_value'
            ]],
            // Test deep nesting
            ['deep_value', ['key' => 'level1.level2.level3'], [
                'level1' => [
                    'level2' => [
                        'level3' => 'deep_value'
                    ]
                ]
            ]],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $settings, $data)   
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $overlay = new ArrayOverlay(1, 30, 90, $settings);

        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
    }

    /**
     * @dataProvider dotNotationProvider
     */
    public function testDotNotationSupport($expectation, $settings, $data)
    {
        $overlay = new ArrayOverlay(1, 30, 90, $settings);
        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
    }
}