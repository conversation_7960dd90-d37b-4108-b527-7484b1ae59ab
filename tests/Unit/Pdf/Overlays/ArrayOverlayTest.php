<?php namespace Tests\Unit\Pdf\Overlays;

use Exception;
use Tests\TestCase;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;

class ArrayOverlayTest extends TestCase
{
    public function constructorProvider()
    {
        return [
            [Exception::class,  [],                  []],
            [Exception::class,  ['foo'],             []],
            ["",  ['key' => 'foo'],    []],
            ["",  ['key' => 'keyName'],    ['badName' => 'keyvalue']],
            ['keyvalue',        ['key' => 'keyName'],    ['keyName' => 'keyvalue']],
            ['',                ['key' => 'keyName'],    ['keyName' => '']],
            [' ',               ['key' => 'keyName'],    ['keyName' => ' ']],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $settings, $data)   
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $overlay = new ArrayOverlay(1, 30, 90, $settings);

        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
    }
}