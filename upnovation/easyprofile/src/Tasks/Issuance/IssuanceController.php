<?php namespace Upnovation\Easyprofile\Tasks\Issuance;

use App\Events\IssuanceProcessed;
use App\Events\PolicyUploaded;
use App\Http\Controllers\Controller;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;


class IssuanceController extends Controller
{
    protected IssuanceManager $manager;

    public function __construct(IssuanceManager $manager)
    {
        $this->manager = $manager;
    }

    public function getIndex(Pipeline $pipeline, Task $task)
    {
        $issuances = $task->issuances()->with('product')->get()->map(function ($issuance) use ($task) {
            $issuance->load('product.company');

            $policyDocument = $issuance->product->getPolicyDocument();

            $issuance->policy = File::whereDocumentId($policyDocument->id)
                ->whereTaskId($task->id)
                ->first();

            return $issuance;
        });
         
        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'issuances' => $issuances,
        ]);
    }

    public function getIssue(Pipeline $pipeline, Task $task, Product $product)
    {
        if (! isset($product->issuanceProcessor['controller'])) {
            Log::error("Issuance processor not defined for product: {$product->name}");

            abort(500, "Issuance processor not defined for product: {$product->name}");
        }

        return redirect()->action([$product->issuanceProcessor['controller'], 'getIndex'], [
            'pipeline' => $pipeline->id,
            'task' => $task->id,
            'product' => $product->id,
        ]);
    }

    public function postIndex(Request $request, Pipeline $pipeline, Task $task)
    {
        $task->addData('issuanceData', $request->all());
        $task->save();

        return redirect()->route('pipeline.next', ['pipeline' => $pipeline->id]);
    }

    public function postUpload(Request $request, Pipeline $pipeline, Task $task)
    {
        if (! $uploaded = $request->file('file')) {
            dd(['file' => 'Nessun file caricato']);
        }

        if (! $issuance = Issuance::find($request->input('issuance_id'))) {
            dd(['issuance' => 'Emissione non trovata']);
        }

        $file = $this->manager->makePolicyFile($issuance);

        dd($this->manager->savePolicyFile(
            $issuance,
            $file,
            file_get_contents($uploaded->getRealPath())
        ));

        /*if (! $product = $issuance->product) {
            dd(['product' => 'Prodotto non trovato']);
        }*/

        if ($issuance->status != 'awaiting') {
            //dd($issuance);
        }
dd('refactor');
        // Leggi il contenuto del file
        $data = file_get_contents($uploaded->getRealPath());

        $policyDocument = $product->documents()->whereType('product-policy')->first();

        // placeholder - delete filesystem too
$formDocument = $product->getFormDocument();
File::whereDocumentId($formDocument->id)->whereTaskId($task->id)->delete();
File::whereDocumentId($policyDocument->id)->whereTaskId($task->id)->delete();

        $file = new File([
            'task_id' => $task->id,
            'document_id' => $policyDocument->id,
            'type' => 'signable',
            'disk' => 'documents',
            'path' => 'compiled',
            'filename' => "policy-" . date('Ymd-His') . "-{$pipeline->id}-{$task->id}.pdf",
        ]);

        $manager = app()->make(FileManager::class);

        $file = $manager->save(
            app('currentTenant')->name, 
            $file, 
            $data
        );

        $issuance->status = 'completed';
        $issuance->save();

        $this->manager->updateTaskCompletion($task);

        event(new PolicyUploaded($issuance));

        return redirect()->back()->with('success', 'File caricato con successo');

    }

    // todo
    public function disable(Issuance $issuance)
    {
        $issuance->status = 'disabled';
        $issuance->save();
        return redirect()->back();
    }

}
