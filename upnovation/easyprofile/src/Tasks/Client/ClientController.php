<?php namespace Upnovation\Easyprofile\Tasks\Client;

use App\Http\Controllers\Controller;
use App\Models\Address;
use App\Models\Client;
use App\Models\Coverage;
use App\Models\Enterprise;
use App\Models\Form;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Profile;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Spatie\Multitenancy\Models\Tenant;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;


class ClientController extends Controller
{
    /** @var ClientManager */
    protected $manager;

    public function __construct(ClientManager $manager)
    {
        $this->manager = $manager;
    }

    public function getIndex(Pipeline $pipeline, Task $task)
    {
        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'client' => $task->pipeline->client,
        ]);
    }

    public function postIndex(Request $request, Pipeline $pipeline, Task $task)
    {
       // dd($request->all());

        // @TODO cleanup this mess
        $rules = $this->getValidationRules($request);

        // @fixme VALIDAZIONE ROTTA PER AZIENDE
        $fileRules = $this->getValidationRulesForTenant(app('currentTenant')->name)['identityDocument'];

    $rules = array_merge($rules, $fileRules);
        //dd($rules);
        try {

            $request->validate($rules);
            
        } catch (ValidationException $ex) {
            Log::debug($ex->getMessage());
            
            return redirect()->back()->withErrors($ex->errors());
        }
        
        // @todo try catch / don't create client if error
        $client = $this->manager->create($pipeline, $request->all(), $request->file('person.documents.id.file'));

        $task->addData('preferences', $request->get('person')['preferences']);
        $task->save();

        return redirect()->route("tasks.client", ['pipeline' => $pipeline->id, 'task' => $task->id]);

        //return redirect()->route("pipeline.next", ['pipeline' => $pipeline->id]);
    }

    protected function getValidationRulesForTenant($tenant)
    {
        $rules = config('easyprofile.rules.default.client');

        if ($tenantOverrides = config("easyprofile.tenants.{$tenant}.tasks.client.rules")) {
            return array_merge($rules, $tenantOverrides);
        }

        return $rules;
    }

    protected function getValidationRules(Request $request)
    {
        $rules = $this->getValidationRulesForTenant(app('currentTenant')->name);

        if (! $request->get('isCorporate')) {
            return $rules['person'];
        }

        if (! $person = $request->get('person')) {
            Log::error("Person data is empty.");

            abort(400);
        }

        if (! empty($person['id'])) {
            return $rules['enterprise'];
        } 
        
        return array_merge($rules['person'], $rules['enterprise']);
    }

    public function putIndex(Request $request, Pipeline $pipeline, Task $task)
    {
        $entity = $request->get('entity');

        if (! $entity['id']) {
            Log::error("Entity model ID is empty.");

            abort(400);
        }

        if (! $entity['type']) {
            Log::error("Entity type is empty.");

            abort(400);
        }

        $client = null;

        if ($entity['type'] == 'individual') {

            $client = Person::find($entity['id']);

        } elseif ($entity['type'] == 'legal') {

            $client = Enterprise::find($entity['id']);
            
        } else {
            Log::error("Entity is not individual nor enterprise");

            return response()->json(['x' => __('easyprofile.error')], 400);
        }

        if (! $client) {
            Log::error("Client model not found.");

            return response()->json(['x' => __('easyprofile.error')], 400);
        }

        return $this->manager->bind($pipeline, $client, 'contractor');
    }

    public function getSearch(Request $request, $query)
    {
        //$query = $request->get('query');

        $persons = Person::where('name', 'like', "%$query%")
            ->orWhere('lastname', 'like', "%$query%")
            ->orWhere('taxCode', 'like', "%$query%")
            ->with('addresses')
            ->get()
            ->map(function ($person) {
                $person->setRelation('addresses', $person->addresses->keyBy('type'));

                return [
                    'id' => $person->id,
                    'name' => (string)$person,
                    'code' => $person->taxCode,
                    'type' => 'individual',
                    'model' => $person,
                ];
            });

        if ($request->get('type') == 'individual') {
            return $persons;
        }

        $enterprises = Enterprise::where('name', 'like', "%$query%")
            ->orWhere('vat', 'like', "%$query%")
            ->get()
            ->map(function ($enterprise) {
                return [
                    'id' => $enterprise->id,
                    'name' => $enterprise->name,
                    'code' => $enterprise->vat,
                    'type' => 'legal',
                    'model' => $enterprise
                ];
            });

        if ($request->get('type') == 'legal') {
            return $enterprises;
        }

        return response()->json($persons->concat($enterprises));
    }

    public function postUpload(Request $request)
    {
        // See route definition in routes/web.php for comment.
        abort(501, 'Not implemented yet.');

        $rules = $this->getValidationRulesForTenant(app('currentTenant')->name)['identityDocument'];

        try {

            //$request->validate($rules);
            
        } catch (ValidationException $ex) {
            Log::debug($ex->getMessage());
            
            return redirect()->back()->withErrors($ex->errors());
        }

        return redirect()->back();// maybe a 200 OK is enough. ->with('foo', 'bar');
    }
}
