<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;

class ArrayOverlay extends AbstractOverlay implements InjectableInterface
{
    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y);
     
        if (empty($settings) || empty($settings['key'])) {
            throw new \Exception("Settings cannot be empty for ArrayOverlay");
        }

        $this->settings = $settings;
    }

    public function inject($data)
    {
        $this->value = (array)$data;
    }

    public function getValue() : string
    {
        if (! isset($this->value[$this->settings['key']])) {
            Log::warning("Key {$this->settings['key']} does not exist in value for ArrayOverlay");
        }

        $rawValue = $this->value[$this->settings['key']];

        // Currency formatting
        if (
            isset($this->settings['fmtCurrency']) &&
            $this->settings['fmtCurrency'] === true &&
            is_numeric($rawValue)
        ) {
            return number_format((float)$rawValue, 2, ',', '.');
        }

        // Date formatting: from YYYY-MM-DD to DD-MM-YYYY
        if (
            isset($this->settings['fmtDate']) &&
            $this->settings['fmtDate'] === true &&
            is_string($rawValue) &&
            preg_match('/^\d{4}-\d{2}-\d{2}$/', $rawValue)
        ) {
            $parts = explode('-', $rawValue);
            if (count($parts) === 3) {
                return $parts[2] . '-' . $parts[1] . '-' . $parts[0];
            }
        }

        return (string)$rawValue;
    }

	public function resolve(Pipeline $pipeline)
    {
        // Do nothing. Work with injected data.
    }

}