<script>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import { router } from "@inertiajs/core";
import { ref } from 'vue';
import Button from 'primevue/button';
import Message from 'primevue/message';
import ToggleSwitch from 'primevue/toggleswitch';
import Tooltip from 'primevue/tooltip';
import Tenant from '../../Components/Tenant.vue';
import InputNumber from 'primevue/inputnumber';
import { useForm } from '@inertiajs/vue3';
import FormMessage from '../../Components/Form/FormMessage.vue';
import InputText from 'primevue/inputtext';

const env = import.meta.env;

export default {
    props: {
        pipeline: Object,
        task: Object,
        issuances: Array,
    },

    components: {
        PipelineLayout,
        Button,
        Message,
        ToggleSwitch,
        Tenant,
        InputNumber,
        FormMessage,
        InputText,
    },

    directives: {
        tooltip: Tooltip,
    },

    data() {
        return {
            loading: ref(false),
            selectedProduct: null,
            hoveredProduct: null,
            toggled: {},
            processType: {
                'direct': 'Immediato',
                'deferred': 'Differito',
                'download': 'Download',
            },
            form: useForm({
                fee: this.task.issuanceData?.fee || null,
            }),
        }
    },

    setup() {
        return {
            env
        }
    },  

    methods: {
        next: function() {
            this.loading = true;
            
            this.form.post(`/tasks/issuance/${this.pipeline.id}/${this.task.id}`, {
                onFinish: () => { this.loading = false }
            });
        },
        toggleProduct(issuanceId) {
            this.toggled[issuanceId] = !this.toggled[issuanceId];
        },
        go: function(issuanceId) {
            router.get(`/issue/${this.pipeline.id}/${this.task.id}/${issuanceId}`, {}, {
                preserveState: true,
                preserveScroll: true,
            });
        },
        getProcessTypeTooltip(type) {
            switch (type) {
                case 'direct':
                    return 'Per polizze ad adesione. Compila il modulo e procedi con la firma al cliente.';
                case 'deferred':
                    return 'Processo differito. Compila il modulo web e attendi il caricamento della proposta assicurativa da parte del backoffice.';
                case 'download':
                    return 'Scarica, compila e ricarica il pdf compilabile, poi attendi il caricamento della proposta assicurativa da parte del backoffice.';
            }
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <form @submit.prevent="next()">
        <div class="flex flex-col gap-5">

            <div class="card w-full">
                <div class="card-body !p-6">
                    <h2 class="mb-2">Prodotti selezionati</h2>
                    <Message severity="info" class="mb-4">
                        Completa il processo per ognuno dei prodotti elencati prima di passare allo step di firma elettronica.
                    </Message>
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="bg-gray-100">
                                <th width="20%" class="text-left px-3 py-2">Compagnia</th>
                                <th width="20%" class="text-left px-3 py-2">Nome prodotto</th>
                                <th class="text-left px-3 py-2 text-center">Tipo di processo</th>
                                <th class="text-left px-3 py-2 text-center">Stato</th>
                                <th class="text-left px-3 py-2 text-center">N. Proposta</th>
                                <th class="text-left px-3 py-2 text-center">N. Polizza</th>
                                <th class="text-left px-3 py-2"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="issuance in issuances" :key="issuance.id">
                                <td class="px-3 py-2 align-middle">
                                    <div class="flex items-center gap-3">
                                        <img :src="'/assets/companies/' + issuance.product.company.logo" alt="" class="h-8 w-8 rounded-full">
                                        <span>{{ issuance.product.company.name }}</span>
                                    </div>
                                </td>
                                <td class="px-3 py-2 align-middle">
                                    <a class="m-0" :href="`/tasks/issuance/${pipeline.id}/${task.id}/product/${issuance.product.id}`">
                                        {{ issuance.product.name }}
                                    </a>
                                </td>
                                <td class="px-3 py-2 align-middle text-center">
                                    <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="getProcessTypeTooltip(issuance.product.processType)">
                                        {{ processType[issuance.product.processType] }}
                                    </span>
                                </td>
                                <td class="px-3 py-2 align-middle text-center">
                                    <span v-if="issuance.status === 'completed'" class="text-green-600">
                                        <span class="mr-2" v-tooltip.top="'Puoi procedere con la firma elettronica'">
                                            <i class="pi pi-check text-xs"></i>
                                        </span>
                                        <br>
                                        <a :href="`/documents/${issuance.policy?.uuid}`">Scarica documento</a>
                                    </span>
                                    <span v-if="issuance.status === 'awaiting'" class="text-yellow-600">
                                        <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="'Attendi che il backoffice carichi la proposta assicurativa'">
                                            <i class="pi pi-hourglass text-xs"></i>
                                        </span>
                                        <br>
                                        <span>In attesa di caricamento</span>
                                    </span>
                                    <span v-if="issuance.status === 'pending'" class="text-red-600">
                                        <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="'Completa il processo per questo prodotto'">
                                            <i class="pi pi-spinner text-xs"></i>
                                        </span>
                                        <br>
                                        <span>Non completato</span>
                                    </span>
                                </td>
                                <td></td>
                                <td></td>
                                <td class="px-3 py-2 align-middle text-right">
                                    <Button
                                        type="button"
                                        label="Compila"
                                        class="p-button-sm"
                                        severity="contrast"
                                        :disabled="issuance.product.processType == 'deferred' && issuance.status !== 'pending'"
                                        @click="go(issuance.id)"
                                        
                                    >
                                    </Button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <Tenant name="simply">
                <div class="card w-full">
                    <div class="card-body !p-6">
                        <h2 class="mb-2">Importo di consulenza</h2>
                        <Message severity="info" class="mb-4">
                            Imposta la fee da richiedere al cliente per lo svolgimento delle attività di consulenza in ambito assicurativo.
                        </Message>

                        <div>
                            <InputText
                                v-model="form.fee"
                                placeholder="Inserisci l'importo"
                                class="w-1/4 h-12"
                                required
                            />
                                
                            <FormMessage :errors="errors" field="fee">Inserisci l'importo. Può essere pari a zero.</FormMessage>
                        </div>
                        
                    </div>
                </div>

                <div class="flex justify-end">
                    <Button v-if="task.data['taskCompleted']" type="submit" label="Avanti" :loading="loading"></Button>
                    <Button v-else type="button" label="Avanti" :loading="loading" disabled v-tooltip.top="'Completa il processo per ognuno dei prodotti elencati prima di passare allo step successivo.'"></Button>
                </div>
            </Tenant>
            
        </div>
        </form>
    </PipelineLayout>
</template>
