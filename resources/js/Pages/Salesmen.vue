<template>
    <MasterLayout>
        <div class="card">

            <div class="card-header">
                <Toolbar style="border: 0">
                    <template #start>
                        <h1 class="mb-0">Elenco collaboratori</h1>
                    </template>
                </Toolbar>
            </div>

            <div class="card-body">
                <div class="max-md:overflow-x-auto pb-10">
                    <DataTable 
                            dataKey="id"
                            :value="list"
                            v-model:filters="filters" 
                            filterDisplay="menu" 
                            :globalFilterFields="['name', 'lastname', 'email']"
                            paginator 
                            :rows="50" 
                            tableStyle="min-width: 50rem" 
                            class="text-sm"
                            >

                            <template #header>
                                <div class="flex justify-end">
                                    <IconField>
                                        <InputIcon>
                                            <i class="pi pi-search" />
                                        </InputIcon>
                                        <InputText v-model="filters['global'].value" placeholder="Cerca ovunque..." />
                                    </IconField>
                                </div>
                            </template>

                        <Column field="id" header="#" sortable></Column>
                        <Column field="node.name" header="Nodo rete" sortable style="width: 15%;"></Column>
                        <Column field="rui" header="RUI" sortable style="width: 15%;">
                            <template #body="data">
                                <span v-if="data.data.rui">
                                    {{ data.data.rui.section }}{{ data.data.rui.code }}
                                </span>
                            </template>
                        </Column>
                        <Column field="lastname" header="Nominativo" sortable style="width: 25%;">
                            <template #body="data">
                                <a :href="`/salesmen/${data.data.id}`">{{ data.data.name }} {{ data.data.lastname }}</a> 
                            </template>
                            <template #filter="{ filterModel, filterCallback }">
                                <InputText v-model="filterModel.value" size="small" type="text"  placeholder="Cognome" />
                            </template>
                        </Column>
                        <Column field="email" header="Email" sortable style="width: 15%;"></Column>
                        <Column field="active" header="Stato" sortable>
                            <template #body="data">
                                <Badge v-if="data.data.active" text="Attivo" color="success"></Badge>
                                <Badge v-else text="Disattivato" color="warning"></Badge>
                            </template>
                        </Column>
                        <Column field="" header="Azioni">
                            <template #body="data">
                                <button class="btn btn-sm btn-icon btn-info" type="button" @click="toggleSalesmanStatus(data.data.id, data.data.active)" :class="{'cursor-not-allowed': data.updating}">
                                    <span v-if="data.data.active && ! data.data.updating">Disattiva</span>
                                    <span v-if="! data.data.active && ! data.data.updating">Attiva</span>
                                    <span v-if="data.data.updating" class="loader-mini"></span>
                                </button>
                            </template>
                        </Column>
                        <Column field="last_login_at" header="Ultimo login" sortable>
                            <template #body="data">
                                {{ data.data.last_login_at ? dateFormatter(data.data.last_login_at) : "-" }}
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>
        </div>
    </MasterLayout>

</template>

<script>
import MasterLayout from '../Layouts/MasterLayout.vue';
import {dateFormatter} from "@/dateFormatter";
import Toolbar from 'primevue/toolbar';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import { ref } from 'vue';
import { directive } from 'vue-tippy'
import {router} from "@inertiajs/core";
import Badge from '../Components/Badge.vue';

export default {
    props: {
        salesmen: Object
    
    },
    directives: {
        tippy: directive,
    },
    data() {
        return {
            list: ref(this.salesmen),

            filters: {
                'global': {
                    value: '',
                    matchMode: 'contains'
                },
                'name': {
                    value: '',
                    matchMode: 'contains'
                },
                'lastname': {
                    value: '',
                    matchMode: 'contains'
                },
            }
        }
    },
    methods: {
        dateFormatter,

        toggleSalesmanStatus(salesmanId, isActive) {
            if (confirm("Sei sicuro di voler cambiare lo stato di questo utente?")) {
                let index = this.list.findIndex((s) => s.id === salesmanId);
                this.list[index].updating = true;
                router.post('/salesman/status/' + salesmanId, {currentStatus: isActive}, {
                    only: ["salesmen"],  
                    preserveState: true, 
                    preserveScroll: true, 
                    onSuccess: visit => {
                        this.list[index] = this.salesmen.find(salesman => salesman.id === salesmanId);
                    }, 
                    onFinish: () => {
                        this.list[index].updating = false; 
                    }
                })
            }
        }
    },
    components: {
        MasterLayout,
        Toolbar,
        DataTable,
        Column,
        InputText,
        Badge
    },
}
</script>

