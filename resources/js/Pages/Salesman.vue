
<script setup>
import MasterLayout from '../Layouts/MasterLayout.vue';
import State from '../Components/State.vue';

defineProps({
    salesman: Object,
    network: Object
})
</script>

<template>
    <MasterLayout>

        <!-- make a grid with 2 columns with ratio 3/9 (tailwind) -->
        <div class="grid grid-cols-12 gap-3">
            <div class="col-span-3 border-2 rounded-lg shadow-md p-5 bg-white">
                <h2 class="font-bold">{{ salesman.name }} {{ salesman.lastname }}</h2>
                <div>
                    {{ salesman.node.name }}
                    <div class="text-sm">
                        {{ network.map(node => node.name).join(', ') }}
                    </div>
                </div>

                <h3 class="font-bold mt-3">Contatti</h3>
                <div>
                    <hr class="my-1">
                    {{ salesman.email }}
                </div>

            </div>
            <div class="col-span-9 border-2 rounded-lg shadow-md p-5 bg-white">
                <h2 class="font-bold mb-3">{{ salesman.pipelines.length }} Posizioni</h2>

                <table class="table-auto">
                    <thead>
                        <tr>
                            <th class="w-3/12 text-left">Stato</th>
                            <th class="w-3/12 text-left">Fase</th>
                            <th class="w-5/12 text-left">Data di creazione</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="p in salesman.pipelines">
                            <td><State :pipeline="p"></State></td>
                            <td>{{ p.currentTask?.displayName }}</td>
                            <td>{{ p.created_at }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </MasterLayout>

</template>

<script>
import { directive } from 'vue-tippy'
import {router} from "@inertiajs/core";
import Badge from '../Components/Badge.vue';
export default {
    directives: {
        tippy: directive,
    },
    methods: {
        toggleSalesmanStatus(salesmanId, isActive) {
            if (confirm("Sei sicuro di voler cambiare lo stato di questo utente?")) {
                let index = this.salesmen.findIndex((s) => s.id === salesmanId);
                this.salesmen[index].updating = true;
                router.post('/salesman/status/' + salesmanId, {currentStatus: isActive}, {only: ["salesmen"],  preserveState: true, preserveScroll: true,  onFinish: visit => {this.salesmen[index].updating = false;},})
            }
        }
    }
}
</script>

