<script>
import AutoComplete from 'primevue/autocomplete';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import axios from 'axios';
import { ref, watch } from 'vue';
import FormMessage from './FormMessage.vue';
import countries from 'world-countries';
import FormUtils from '../../form.utils';

export default {
    props: {
        birthplace: Object,
        country: String,
        class: Object,
        errors: Object,
    },

    components: {
        InputText,
        AutoComplete,
        Select,
        FormMessage,
    },

    data() {
        return {
            geo: ref({}),
            suggestions: ref([]),
            countries: countries,
            countryValue: ref(this.country || countries.find(c => c.cca2 === 'IT')),
            countrySuggestions: ref([]),
        }
    },

    computed: {
        filteredErrors() {
            return new FormUtils().filterErrors(this.errors, "person.birthplace");
        }
    },

    watch: {
        geo: {
            deep: true,
            handler(newGeo) {
                if (typeof newGeo === 'object' && newGeo !== null && newGeo.denominazione_provincia) {
                    this.birthplace.province = newGeo.denominazione_provincia;
                }
                this.emitBirthplace();
            }
        },
        countryValue: {
            deep: true,
            handler() {
                this.emitBirthplace();
            }
        },
        'birthplace.province': {
            handler() {
                this.emitBirthplace();
            }
        }
    },

    methods: {
        async searchMunicipality(event) {
            if (!event.query.length || event.query.length < 3) {
                this.suggestions = [];
                return;
            }
            await axios.get(`/geo/${event.query}?_`).then(response => {
                this.suggestions = response.data;
            });
        },
        searchCountry(event) {
            const query = event.query ? event.query.toLowerCase() : '';
            this.countrySuggestions = this.countries.filter(c =>
                c.translations?.ita?.common?.toLowerCase().includes(query)
            );
        },
        emitBirthplace() {
            this.$emit('update:birthplace', {
                country: typeof this.countryValue === 'object' && this.countryValue !== null
                    ? this.countryValue.translations.ita.common
                    : this.countryValue,
                city: typeof this.geo === 'object' && this.geo !== null
                    ? this.geo.denominazione_ita
                    : this.geo,
                province: this.birthplace.province,
            });
        },
        hasErrors() {
            return this.filteredErrors && Object.keys(this.filteredErrors).length;
        }
    },
}
</script>

<template>
    <div class="flex gap-1" :class="{'error-border rounded-lg p-5': hasErrors()}">

        <div>
            <AutoComplete
                
                optionLabel="translations.ita.common"
                v-model="countryValue"
                :suggestions="countrySuggestions"
                @complete="searchCountry"
                placeholder="Seleziona nazione"
                @keydown.enter.stop.prevent
            >
                <template #option="slotProps">
                    <div>
                        {{ slotProps.option.translations.ita.common }}
                    </div>
                </template>
            </AutoComplete>

            <FormMessage :errors="filteredErrors" field="person.birthplace.country">Seleziona la nazione di nascita.</FormMessage>
        </div>
        

        <div>
            <AutoComplete
                optionLabel="denominazione_ita"
                v-model="geo"
                :suggestions="suggestions"
                @complete="searchMunicipality"
                placeholder="Cerca città"
                @keydown.enter.stop.prevent
            >
                <template #option="slotProps">
                    <div>
                        <div>{{ slotProps.option.denominazione_ita }}</div>
                        <small>{{ slotProps.option.cap }}</small>
                    </div>
                </template>
            </AutoComplete>

            <FormMessage :errors="filteredErrors" field="person.birthplace.city">Seleziona la città di nascita.</FormMessage>
        </div>
        
        <div>
            <!-- Disable if country is Italy-->
            <InputText
                v-model="birthplace.province"
                placeholder="Provincia"
                :disabled="countryValue?.cca2 == 'IT'"
            />
            <FormMessage :errors="filteredErrors" field="person.birthplace.province">Seleziona la provincia.</FormMessage>
        </div>
        
    </div>
  
</template>
