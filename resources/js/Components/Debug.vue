<script>
import Tooltip from 'primevue/tooltip';

const env = import.meta.env;

let _debugLevel = 0;

export default {
    props: {
        float: Boolean
    },
    directives: {
        tooltip: Tooltip,
    },
    setup() {

        return {
            env,
            level: (100 + (_debugLevel++ * 60)) + "px" ,
        }
    }
};
</script>

<template>
    <template v-if="env.DEV">
        <div
            v-tooltip.top="'Questo controllo è visualizzato solo in ambiente di sviluppo.'"
            :class="[
                'flex items-center gap-2 border border-yellow-600 rounded-md p-2 text-gray-800 z-50',
                float ? 'fixed right-9 bg-white shadow-lg' : ''
            ]"
            :style="float ? `bottom: ${level};` : ''"
        >

            <span class="flex items-center">
                <slot />
            </span>
        </div>
    </template>
</template>
